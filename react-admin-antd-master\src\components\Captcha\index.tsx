import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
} from "react";
import { getUUID } from "@/utils/captcha";
import { request } from "@/utils/request";

interface CaptchaProps {
  width?: number;
  height?: number;
  onChange?: (uuid: string) => void;
}

export interface CaptchaRef {
  refreshCaptcha: () => void;
}

const Captcha = forwardRef<CaptchaRef, CaptchaProps>(
  ({ width = 100, height = 40, onChange }, ref) => {
    const [captchaUrl, setCaptchaUrl] = useState("");

    const refreshCaptcha = async () => {
      try {
        const newUuid = getUUID();
        const response = (await request.get(`/captcha.jpg?uuid=${newUuid}`, {
          responseType: "blob",
        })) as Blob;

        const imageUrl = URL.createObjectURL(response);
        setCaptchaUrl(imageUrl);
        onChange?.(newUuid);
      } catch (error) {
        console.error("获取验证码失败:", error);
      }
    };

    useImperativeHandle(ref, () => ({
      refreshCaptcha,
    }));

    useEffect(() => {
      refreshCaptcha();

      // 清理函数，释放blob URL
      return () => {
        if (captchaUrl) {
          URL.revokeObjectURL(captchaUrl);
        }
      };
    }, []);

    return (
      <div
        className="captcha-image-container flex-shrink-0 cursor-pointer"
        onClick={refreshCaptcha}
        title="点击刷新验证码"
      >
        <img
          src={captchaUrl}
          alt="验证码"
          width={width}
          height={height}
          className="h-full w-full object-cover"
        />
      </div>
    );
  }
);

export default Captcha;
// import React, { useEffect, useRef, useState } from "react";
// import { generateCaptcha } from "@/utils/captcha";

// interface CaptchaProps {
//   width?: number;
//   height?: number;
//   onChange?: (code: string) => void;
// }

// const Captcha: React.FC<CaptchaProps> = ({
//   width = 100,
//   height = 40,
//   onChange,
// }) => {
//   const canvasRef = useRef<HTMLCanvasElement>(null);
//   const [, setCaptcha] = useState("");

//   const refreshCaptcha = () => {
//     if (canvasRef.current) {
//       const newCaptcha = generateCaptcha(canvasRef.current);
//       setCaptcha(newCaptcha);
//       onChange?.(newCaptcha);
//     }
//   };

//   useEffect(() => {
//     refreshCaptcha();
//   }, []);

//   return (
//     <div
//       className="captcha-image-container flex-shrink-0 cursor-pointer"
//       onClick={refreshCaptcha}
//       title="点击刷新验证码"
//     >
//       <canvas
//         ref={canvasRef}
//         width={width}
//         height={height}
//         className="h-full w-full"
//       />
//     </div>
//   );
// };

// export default Captcha;
